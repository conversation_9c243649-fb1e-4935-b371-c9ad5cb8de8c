const crypto = require('crypto');
const TikTokKeyGenerator = require('./node-key-generator-clean');

// 新的服务器证书
const newServerCert = `-----BEGIN CERTIFICATE-----
MIIEfTCCBCKgAwIBAgIUXWdS2tzmSoewCWfKFyiWMrJqs/0wCgYIKoZIzj0EAwIw
MTELMAkGA1UEBhMCQ04xIjAgBgNVBAMMGXRpY2tldF9ndWFyZF9jYV9lY2RzYV8y
NTYwIBcNMjIxMTE4MDUyMDA2WhgPMjA2OTEyMzExNjAwMDBaMCQxCzAJBgNVBAYT
AkNOMRUwEwYDVQQDEwxlY2llcy1zZXJ2ZXIwWTATBgcqhkjOPQIBBggqhkjOPQMB
BwNCAASE2llDPlfc8Rq+5J5HXhg4edFjPnCF3Ua7JBoiE/foP9m7L5ELIcvxCgEx
aRCHbQ8kCCK/ArZ4FX/qCobZAkToo4IDITCCAx0wDgYDVR0PAQH/BAQDAgWgMDEG
A1UdJQQqMCgGCCsGAQUFBwMBBggrBgEFBQcDAgYIKwYBBQUHAwMGCCsGAQUFBwME
MCkGA1UdDgQiBCABydxqGrVEHhtkCWTb/vicGpDZPFPDxv82wiuywUlkBDArBgNV
HSMEJDAigCAypWfqjmRIEo3MTk1Ae3MUm0dtU3qk0YDXeZSXeyJHgzCCAZQGCCsG
AQUFBwEBBIIBhjCCAYIwRgYIKwYBBQUHMAGGOmh0dHA6Ly9uZXh1cy1wcm9kdWN0
aW9uLmJ5dGVkYW5jZS5jb20vYXBpL2NlcnRpZmljYXRlL29jc3AwRgYIKwYBBQUH
MAGGOmh0dHA6Ly9uZXh1cy1wcm9kdWN0aW9uLmJ5dGVkYW5jZS5uZXQvYXBpL2Nl
cnRpZmljYXRlL29jc3AwdwYIKwYBBQUHMAKGa2h0dHA6Ly9uZXh1cy1wcm9kdWN0
aW9uLmJ5dGVkYW5jZS5jb20vYXBpL2NlcnRpZmljYXRlL2Rvd25sb2FkLzQ4RjlD
MEU3QjBDNUE3MDVCOTgyQkU1NTE3MDVGNjQ1QzhDODc4QTguY3J0MHcGCCsGAQUF
BzAChmtodHRwOi8vbmV4dXMtcHJvZHVjdGlvbi5ieXRlZGFuY2UubmV0L2FwaS9j
ZXJ0aWZpY2F0ZS9kb3dubG9hZC80OEY5QzBFN0IwQzVBNzA1Qjk4MkJFNTUxNzA1
RjY0NUM4Qzg3OEE4LmNydDCB5wYDVR0fBIHfMIHcMGygaqBohmZodHRwOi8vbmV4
dXMtcHJvZHVjdGlvbi5ieXRlZGFuY2UuY29tL2FwaS9jZXJ0aWZpY2F0ZS9jcmwv
NDhGOUMwRTdCMEM1QTcwNUI5ODJCRTU1MTcwNUY2NDVDOEM4NzhBOC5jcmwwbKBq
oGiGZmh0dHA6Ly9uZXh1cy1wcm9kdWN0aW9uLmJ5dGVkYW5jZS5uZXQvYXBpL2Nl
cnRpZmljYXRlL2NybC80OEY5QzBFN0IwQzVBNzA1Qjk4MkJFNTUxNzA1RjY0NUM4
Qzg3OEE4LmNybDAKBggqhkjOPQQDAgNJADBGAiEAqMjT5ADMdGMeaImoJK4J9jzE
LqZ573rNjsT3k14pK50CIQCLpWHVKWi71qqqrMjiSDvUhpyO1DpTPRHlavPRuaNm
ww==
-----END CERTIFICATE-----`;

// 旧的服务器公钥（用于对比）
const oldServerPublicKey = 'BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=';

// 测试参数
const testParams = {
    ticket: "hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=",
    path: "/aweme/v1/web/comment/publish/",
    timestamp: 1754020144
};

// 客户端密钥
const realKeyPair = {
************************************************************************************************************************************************************************************************************************************************************************
    "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----"
};

// TikTok 的 req_sign（用于对比）
const tiktokReqSign = "aMGGQeXb2lxAOZfBSLmRnwMTI5s9VAWIv+HS5upaLXA=";

function extractPublicKeyFromCert() {
    console.log('🔍 从新服务器证书中提取公钥（按照 TikTok 的方式）\n');

    try {
        // 按照 TikTok req.js 中 N 函数的实现方式
        // 第 2023 行: e = P(t) - 移除 PEM 头尾并转换为 ArrayBuffer
        const pemContent = newServerCert
            .replace(/-----BEGIN CERTIFICATE-----/, "")
            .replace(/-----END CERTIFICATE-----/, "")
            .replace(/\s+/g, "");

        const binaryString = Buffer.from(pemContent, 'base64').toString('binary');
        const arrayBuffer = new ArrayBuffer(binaryString.length);
        const uint8Array = new Uint8Array(arrayBuffer);
        for (let i = 0; i < binaryString.length; i++) {
            uint8Array[i] = binaryString.charCodeAt(i);
        }

        console.log('📋 证书处理:');
        console.log('  PEM 内容长度:', pemContent.length);
        console.log('  二进制长度:', arrayBuffer.byteLength);

        // 使用 Node.js 的 X.509 证书解析（模拟 TikTok 的 ASN.1 解析）
        const cert = new crypto.X509Certificate(newServerCert);

        // 获取公钥并导出为 SPKI 格式
        const publicKey = cert.publicKey;
        const spkiBuffer = publicKey.export({
            type: 'spki',
            format: 'der'
        });

        console.log('\n🔑 公钥信息:');
        console.log('  SPKI 长度:', spkiBuffer.length, '字节');
        console.log('  SPKI (hex):', spkiBuffer.toString('hex'));

        // 按照 TikTok 的方式提取原始公钥点（最后65字节）
        const rawPublicKey = spkiBuffer.slice(-65);
        const newServerPublicKeyBase64 = rawPublicKey.toString('base64');

        console.log('\n📤 提取的公钥:');
        console.log('  原始公钥长度:', rawPublicKey.length, '字节');
        console.log('  原始公钥 (hex):', rawPublicKey.toString('hex'));
        console.log('  Base64 格式:', newServerPublicKeyBase64);

        console.log('\n🔄 与旧公钥对比:');
        console.log('  旧公钥:', oldServerPublicKey);
        console.log('  新公钥:', newServerPublicKeyBase64);
        console.log('  是否相同:', oldServerPublicKey === newServerPublicKeyBase64 ? '✅ 是' : '❌ 否');

        // 验证证书信息
        console.log('\n📋 证书详细信息:');
        console.log('  主题:', cert.subject);
        console.log('  颁发者:', cert.issuer);
        console.log('  有效期从:', cert.validFrom);
        console.log('  有效期到:', cert.validTo);
        console.log('  序列号:', cert.serialNumber);

        return newServerPublicKeyBase64;

    } catch (error) {
        console.error('❌ 提取公钥失败:', error.message);
        console.error('错误堆栈:', error.stack);
        return null;
    }
}

function testWithNewServerKey(newServerPublicKey) {
    console.log('\n🧪 使用新服务器公钥测试签名\n');
    
    if (!newServerPublicKey) {
        console.log('❌ 没有新的服务器公钥，跳过测试');
        return;
    }
    
    try {
        // 使用新服务器公钥生成签名
        const result = TikTokKeyGenerator.generateReqSign(
            realKeyPair.ec_privateKey,
            newServerPublicKey,
            testParams.ticket,
            testParams.path,
            testParams.timestamp
        );
        
        console.log('使用新服务器公钥生成:');
        console.log('  服务器公钥:', newServerPublicKey);
        console.log('  生成的 req_sign:', result.req_sign);
        console.log('  与 TikTok 匹配:', result.req_sign === tiktokReqSign ? '✅ 是' : '❌ 否');
        
        if (result.req_sign === tiktokReqSign) {
            console.log('\n🎉 找到匹配！新服务器公钥是正确的！');
            console.log('✅ 问题解决：TikTok 使用了新的服务器证书');
        } else {
            console.log('\n❌ 仍然不匹配，可能还有其他问题');
        }
        
        // 同时测试旧服务器公钥作为对比
        console.log('\n🔄 对比：使用旧服务器公钥');
        const oldResult = TikTokKeyGenerator.generateReqSign(
            realKeyPair.ec_privateKey,
            oldServerPublicKey,
            testParams.ticket,
            testParams.path,
            testParams.timestamp
        );
        
        console.log('  旧服务器公钥结果:', oldResult.req_sign);
        console.log('  与 TikTok 匹配:', oldResult.req_sign === tiktokReqSign ? '✅ 是' : '❌ 否');
        
        return result;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return null;
    }
}

function updateImplementation(newServerPublicKey) {
    console.log('\n📝 更新建议\n');
    
    if (newServerPublicKey && newServerPublicKey !== oldServerPublicKey) {
        console.log('🔄 需要更新服务器公钥:');
        console.log('');
        console.log('在你的代码中，将服务器公钥从:');
        console.log(`  "${oldServerPublicKey}"`);
        console.log('');
        console.log('更新为:');
        console.log(`  "${newServerPublicKey}"`);
        console.log('');
        console.log('这样就能生成与 TikTok 匹配的 req_sign 了！');
    } else {
        console.log('🤔 服务器公钥没有变化，问题可能在其他地方');
    }
}

// 运行分析
console.log('🚀 开始从新服务器证书中提取公钥\n');

const newServerPublicKey = extractPublicKeyFromCert();
const testResult = testWithNewServerKey(newServerPublicKey);
updateImplementation(newServerPublicKey);

console.log('\n🎯 总结:');
if (newServerPublicKey) {
    console.log('✅ 成功提取新服务器公钥');
    console.log('📋 新公钥:', newServerPublicKey);
    
    if (testResult && testResult.req_sign === tiktokReqSign) {
        console.log('🎉 问题已解决！使用新服务器公钥可以生成正确的签名');
    } else {
        console.log('⚠️ 使用新服务器公钥仍然无法匹配，需要进一步调试');
    }
} else {
    console.log('❌ 提取公钥失败');
}
