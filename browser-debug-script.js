// 在抖音页面的浏览器控制台中运行此脚本
// 用于获取抖音实际使用的密钥数据

console.log('🔍 抖音密钥调试脚本');
console.log('请在抖音页面的浏览器控制台中运行此脚本');
console.log('─'.repeat(50));

// 1. 获取客户端密钥数据
function getClientKeys() {
    console.log('\n📋 1. 获取客户端密钥数据');
    
    try {
        const cryptData = localStorage.getItem('security-sdk/s_sdk_crypt_sdk');
        if (cryptData) {
            const parsed = JSON.parse(cryptData);
            const keyData = JSON.parse(parsed.data);
            
            console.log('✅ 客户端密钥数据:');
            console.log('  私钥长度:', keyData.ec_privateKey.length);
            console.log('  公钥长度:', keyData.ec_publicKey.length);
            console.log('  私钥:', keyData.ec_privateKey);
            console.log('  公钥:', keyData.ec_publicKey);
            
            return keyData;
        } else {
            console.log('❌ 未找到客户端密钥数据');
            return null;
        }
    } catch (error) {
        console.error('❌ 获取客户端密钥失败:', error);
        return null;
    }
}

// 2. 拦截 ECDH 密钥交换过程
function interceptECDH() {
    console.log('\n📋 2. 拦截 ECDH 密钥交换');
    
    // 保存原始的 crypto.subtle.deriveBits 方法
    const originalDeriveBits = crypto.subtle.deriveBits;
    
    // 重写 deriveBits 方法来拦截 ECDH
    crypto.subtle.deriveBits = async function(algorithm, baseKey, length) {
        console.log('🔍 检测到 ECDH 密钥交换:');
        console.log('  算法:', algorithm);
        console.log('  长度:', length);
        
        if (algorithm.name === 'ECDH') {
            console.log('  服务器公钥对象:', algorithm.public);
            
            // 导出服务器公钥
            try {
                const serverPublicKeySpki = await crypto.subtle.exportKey('spki', algorithm.public);
                const serverPublicKeyBytes = new Uint8Array(serverPublicKeySpki);
                const serverPublicKeyPoint = serverPublicKeyBytes.slice(26); // 去掉 SPKI 头部
                
                console.log('  服务器公钥 SPKI 长度:', serverPublicKeyBytes.length);
                console.log('  服务器公钥点长度:', serverPublicKeyPoint.length);
                console.log('  服务器公钥点 (hex):', Array.from(serverPublicKeyPoint).map(b => b.toString(16).padStart(2, '0')).join(''));
                console.log('  服务器公钥点 (base64):', btoa(String.fromCharCode(...serverPublicKeyPoint)));
                
                // 导出客户端私钥
                const clientPrivateKeyPkcs8 = await crypto.subtle.exportKey('pkcs8', baseKey);
                const clientPrivateKeyBytes = new Uint8Array(clientPrivateKeyPkcs8);
                console.log('  客户端私钥 PKCS8 长度:', clientPrivateKeyBytes.length);
                console.log('  客户端私钥 (hex):', Array.from(clientPrivateKeyBytes).map(b => b.toString(16).padStart(2, '0')).join(''));
                
            } catch (error) {
                console.error('  ❌ 导出密钥失败:', error);
            }
        }
        
        // 调用原始方法
        const result = await originalDeriveBits.call(this, algorithm, baseKey, length);
        
        if (algorithm.name === 'ECDH') {
            const sharedSecret = new Uint8Array(result);
            console.log('  🔑 ECDH 共享密钥:');
            console.log('    长度:', sharedSecret.length);
            console.log('    共享密钥 (hex):', Array.from(sharedSecret).map(b => b.toString(16).padStart(2, '0')).join(''));
            console.log('    共享密钥 (base64):', btoa(String.fromCharCode(...sharedSecret)));
            
            // 保存到全局变量供后续使用
            window.debugECDHSharedSecret = sharedSecret;
        }
        
        return result;
    };
    
    console.log('✅ ECDH 拦截器已安装');
}

// 3. 拦截 HMAC 签名过程
function interceptHMAC() {
    console.log('\n📋 3. 拦截 HMAC 签名');
    
    // 保存原始的 crypto.subtle.sign 方法
    const originalSign = crypto.subtle.sign;
    
    // 重写 sign 方法来拦截 HMAC
    crypto.subtle.sign = async function(algorithm, key, data) {
        if (algorithm.name === 'HMAC') {
            console.log('🔍 检测到 HMAC 签名:');
            console.log('  算法:', algorithm);
            
            // 导出 HMAC 密钥
            try {
                const hmacKeyRaw = await crypto.subtle.exportKey('raw', key);
                const hmacKeyBytes = new Uint8Array(hmacKeyRaw);
                console.log('  HMAC 密钥长度:', hmacKeyBytes.length);
                console.log('  HMAC 密钥 (hex):', Array.from(hmacKeyBytes).map(b => b.toString(16).padStart(2, '0')).join(''));
                console.log('  HMAC 密钥 (base64):', btoa(String.fromCharCode(...hmacKeyBytes)));
                
                // 保存到全局变量
                window.debugHMACKey = hmacKeyBytes;
            } catch (error) {
                console.error('  ❌ 导出 HMAC 密钥失败:', error);
            }
            
            // 显示签名数据
            const signData = new Uint8Array(data);
            const signDataString = new TextDecoder().decode(signData);
            console.log('  签名数据长度:', signData.length);
            console.log('  签名数据 (string):', signDataString);
            console.log('  签名数据 (hex):', Array.from(signData).map(b => b.toString(16).padStart(2, '0')).join(''));
        }
        
        // 调用原始方法
        const result = await originalSign.call(this, algorithm, key, data);
        
        if (algorithm.name === 'HMAC') {
            const signature = new Uint8Array(result);
            console.log('  🔐 HMAC 签名结果:');
            console.log('    长度:', signature.length);
            console.log('    签名 (hex):', Array.from(signature).map(b => b.toString(16).padStart(2, '0')).join(''));
            console.log('    签名 (base64):', btoa(String.fromCharCode(...signature)));
            
            // 保存到全局变量
            window.debugHMACSignature = signature;
        }
        
        return result;
    };
    
    console.log('✅ HMAC 拦截器已安装');
}

// 4. 主函数
function startDebugging() {
    console.log('\n🚀 开始调试');
    
    // 获取客户端密钥
    const clientKeys = getClientKeys();
    
    // 安装拦截器
    interceptECDH();
    interceptHMAC();
    
    console.log('\n💡 使用说明:');
    console.log('1. 拦截器已安装，现在请执行会触发 req_sign 生成的操作');
    console.log('2. 比如发表评论、点赞等');
    console.log('3. 观察控制台输出的密钥数据');
    console.log('4. 将输出的数据与您的实现进行对比');
    
    console.log('\n📊 对比数据:');
    console.log('您的 ECDH 共享密钥: ca03d1f276b4c934935d4ebd2135441a4fa96ae945e03afffb8af2500a9240b1');
    console.log('您的 HKDF 派生密钥: fa2f0939dcf3925bbad568c222bacce0424530753654e329919c7d8318fde756');
    console.log('您的 req_sign: dR+8TTF6l/lRmRq7T1hLJeQGfUqML0aCyehEBpzJZQM=');
    console.log('期望的 req_sign: FKpW/Dg8ry/tOJqu/9kHnfHAbzusq1f+YXesc3jkqeU=');
}

// 5. 恢复原始方法的函数
function restoreOriginalMethods() {
    console.log('\n🔄 恢复原始方法');
    location.reload(); // 简单的方法是刷新页面
}

// 启动调试
startDebugging();

// 提供全局函数
window.restoreDebug = restoreOriginalMethods;
