const TikTokKeyGenerator = require('./node-key-generator-clean');

// 你提供的真实密钥对
const realKeyPair = {
************************************************************************************************************************************************************************************************************************************************************************
    "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----",
    "ec_csr": ""
};

// TikTok 服务器公钥 (从证书中提取)
const serverPublicKeyBase64 = 'BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=';

function testCleanVersion() {
    console.log('🚀 测试干净版本的 TikTok 密钥生成器 - 使用真实数据\n');

    // 真实数据参数 - 正确的数据
    const realData = {
        "ticket": "hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=",
        "ts_sign": "ts.2.c893b1247d49b4486e2b446186ba3a3d4481d83f32335752a07a9af112b36bbfc4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d",
        "client_cert": "pub.BAYnZvHvp0G0t5hQB7/9w+/dvLOlo3cLeAFaOAiolNnc+zOVRwpAtyDG5HWTSfbX+0tzpwkIIZdXebIzGreR8RQ=",
        "log_id": "2025080111490440F39B49574A8E7E21E0",
        "create_time": 1754026792
    };

    // 测试参数 - 使用真实数据
    const ticket = realData.ticket;
    const path = 'aweme/v1/web/comment/publish';
    const timestamp = realData.create_time; // 使用秒级时间戳 (1754013109)
    const tsSign = realData.ts_sign;
    
    console.log('📋 真实测试参数:');
    console.log('  Ticket:', ticket);
    console.log('  Path:', path);
    console.log('  Timestamp:', timestamp);
    console.log('  TS Sign:', tsSign);
    console.log('  Log ID:', realData.log_id);
    console.log('');
    
    try {
        // 1. 验证公钥提取
        console.log('🔍 验证公钥提取...');
        const extractedPublicKey = TikTokKeyGenerator.extractRawPublicKey(realKeyPair.ec_publicKey);
        const expectedPublicKey = 'BAYnZvHvp0G0t5hQB7/9w+/dvLOlo3cLeAFaOAiolNnc+zOVRwpAtyDG5HWTSfbX+0tzpwkIIZdXebIzGreR8RQ=';
        
        console.log('  提取的公钥:', extractedPublicKey);
        console.log('  期望的公钥:', expectedPublicKey);
        console.log('  ✅ 公钥匹配:', extractedPublicKey === expectedPublicKey ? '是' : '否');
        console.log('');
        
        // 2. 生成 Bd-Ticket-Guard-Client-Data (使用真实的 ts_sign)
        console.log('🎯 生成 Bd-Ticket-Guard-Client-Data (使用真实数据)...');
        const result = TikTokKeyGenerator.generateBdTicketGuardClientData(
            realKeyPair.ec_privateKey,
            realKeyPair.ec_publicKey,
            serverPublicKeyBase64,
            ticket,
            path,
            timestamp,
            tsSign  // 使用真实的 ts_sign
        );
        
        console.log('\n📊 生成结果:');
        console.log('🔐 Bd-Ticket-Guard-Client-Data:');
        console.log('  ', result.bdTicketGuardClientData);
        console.log('');
        console.log('🔑 客户端公钥 (用于请求头):');
        console.log('  ', result.clientPublicKeyBase64);
        console.log('');
        console.log('📄 客户端数据结构:');
        console.log('  ', JSON.stringify(result.clientData, null, 2));
        console.log('');
        
        // 3. 验证数据完整性
        console.log('🔍 验证数据完整性...');
        const decoded = JSON.parse(Buffer.from(result.bdTicketGuardClientData, 'base64').toString('utf8'));
        const requiredFields = ['ts_sign', 'req_content', 'req_sign', 'timestamp'];
        const hasAllFields = requiredFields.every(field => field in decoded);
        
        console.log('  必需字段:', requiredFields.join(', '));
        console.log('  实际字段:', Object.keys(decoded).join(', '));
        console.log('  ✅ 字段完整:', hasAllFields ? '是' : '否');
        console.log('');
        
        // 4. 输出使用说明
        console.log('📋 使用说明:');
        console.log('');
        console.log('在发送请求时，需要添加以下内容:');
        console.log('');
        console.log('请求头:');
        console.log(`  "bd-ticket-guard-ree-public-key": "${result.clientPublicKeyBase64}"`);
        console.log('');
        console.log('请求体参数:');
        console.log(`  "Bd-Ticket-Guard-Client-Data": "${result.bdTicketGuardClientData}"`);
        console.log('');
        
        // 5. 生成新密钥对测试
        console.log('🆕 测试生成新密钥对...');
        const newKeyPair = TikTokKeyGenerator.generateKeyPair();
        console.log('  ✅ 新密钥对生成成功');
        console.log('  算法:', newKeyPair.algorithm);
        console.log('  曲线:', newKeyPair.curve);
        console.log('  时间戳:', newKeyPair.timestamp);
        console.log('');
        
        console.log('🎉 所有测试完成！');
        
        return result;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
        return null;
    }
}

// 运行测试
testCleanVersion();
