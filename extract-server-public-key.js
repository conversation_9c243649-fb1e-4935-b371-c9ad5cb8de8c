const crypto = require('crypto');

// 抖音服务器证书
const serverCert = `-----BEGIN CERTIFICATE-----
MIIEfTCCBCKgAwIBAgIUXWdS2tzmSoewCWfKFyiWMrJqs/0wCgYIKoZIzj0EAwIw
MTELMAkGA1UEBhMCQ04xIjAgBgNVBAMMGXRpY2tldF9ndWFyZF9jYV9lY2RzYV8y
NTYwIBcNMjIxMTE4MDUyMDA2WhgPMjA2OTEyMzExNjAwMDBaMCQxCzAJBgNVBAYT
AkNOMRUwEwYDVQQDEwxlY2llcy1zZXJ2ZXIwWTATBgcqhkjOPQIBBggqhkjOPQMB
BwNCAASE2llDPlfc8Rq+5J5HXhg4edFjPnCF3Ua7JBoiE/foP9m7L5ELIcvxCgEx
aRCHbQ8kCCK/ArZ4FX/qCobZAkToo4IDITCCAx0wDgYDVR0PAQH/BAQDAgWgMDEG
A1UdJQQqMCgGCCsGAQUFBwMBBggrBgEFBQcDAgYIKwYBBQUHAwMGCCsGAQUFBwME
MCkGA1UdDgQiBCABydxqGrVEHhtkCWTb/vicGpDZPFPDxv82wiuywUlkBDArBgNV
HSMEJDAigCAypWfqjmRIEo3MTk1Ae3MUm0dtU3qk0YDXeZSXeyJHgzCCAZQGCCsG
AQUFBwEBBIIBhjCCAYIwRgYIKwYBBQUHMAGGOmh0dHA6Ly9uZXh1cy1wcm9kdWN0
aW9uLmJ5dGVkYW5jZS5jb20vYXBpL2NlcnRpZmljYXRlL29jc3AwRgYIKwYBBQUH
MAGGOmh0dHA6Ly9uZXh1cy1wcm9kdWN0aW9uLmJ5dGVkYW5jZS5uZXQvYXBpL2Nl
cnRpZmljYXRlL29jc3AwdwYIKwYBBQUHMAKGa2h0dHA6Ly9uZXh1cy1wcm9kdWN0
aW9uLmJ5dGVkYW5jZS5jb20vYXBpL2NlcnRpZmljYXRlL2Rvd25sb2FkLzQ4RjlD
MEU3QjBDNUE3MDVCOTgyQkU1NTE3MDVGNjQ1QzhDODc4QTguY3J0MHcGCCsGAQUF
BzAChmtodHRwOi8vbmV4dXMtcHJvZHVjdGlvbi5ieXRlZGFuY2UubmV0L2FwaS9j
ZXJ0aWZpY2F0ZS9kb3dubG9hZC80OEY5QzBFN0IwQzVBNzA1Qjk4MkJFNTUxNzA1
RjY0NUM4Qzg3OEE4LmNydDCB5wYDVR0fBIHfMIHcMGygaqBohmZodHRwOi8vbmV4
dXMtcHJvZHVjdGlvbi5ieXRlZGFuY2UuY29tL2FwaS9jZXJ0aWZpY2F0ZS9jcmwv
NDhGOUMwRTdCMEM1QTcwNUI5ODJCRTU1MTcwNUY2NDVDOEM4NzhBOC5jcmwwbKBq
oGiGZmh0dHA6Ly9uZXh1cy1wcm9kdWN0aW9uLmJ5dGVkYW5jZS5uZXQvYXBpL2Nl
cnRpZmljYXRlL2NybC80OEY5QzBFN0IwQzVBNzA1Qjk4MkJFNTUxNzA1RjY0NUM4
Qzg3OEE4LmNybDAKBggqhkjOPQQDAgNJADBGAiEAqMjT5ADMdGMeaImoJK4J9jzE
LqZ573rNjsT3k14pK50CIQCLpWHVKWi71qqqrMjiSDvUhpyO1DpTPRHlavPRuaNm
ww==
-----END CERTIFICATE-----`;

function extractPublicKeyFromCert() {
    console.log('🔍 从抖音服务器证书中提取公钥\n');
    
    try {
        // 1. 解析证书
        const cert = crypto.createPublicKey({
            key: serverCert,
            format: 'pem',
            type: 'cert'
        });
        
        console.log('✅ 证书解析成功');
        
        // 2. 导出为 SPKI 格式
        const spkiPublicKey = cert.export({
            format: 'der',
            type: 'spki'
        });
        
        console.log('📋 证书信息:');
        console.log('  格式: X.509 证书');
        console.log('  算法: ECDSA P-256');
        console.log('  颁发者: ticket_guard_ca_ecdsa_256');
        console.log('  主题: ecies-server');
        console.log('');
        
        // 3. 提取原始公钥点（去掉 SPKI 头部）
        // SPKI 头部长度为 26 字节，公钥点为 65 字节（04 + 32字节x + 32字节y）
        const publicKeyPoint = spkiPublicKey.slice(26);
        
        console.log('🔑 提取的公钥信息:');
        console.log('  SPKI DER 长度:', spkiPublicKey.length, '字节');
        console.log('  公钥点长度:', publicKeyPoint.length, '字节');
        console.log('  公钥点 (hex):', publicKeyPoint.toString('hex'));
        console.log('  公钥点 (base64):', publicKeyPoint.toString('base64'));
        console.log('');
        
        // 4. 验证公钥格式
        if (publicKeyPoint.length === 65 && publicKeyPoint[0] === 0x04) {
            console.log('✅ 公钥格式验证通过:');
            console.log('  格式: 未压缩点 (04 前缀)');
            console.log('  X 坐标:', publicKeyPoint.slice(1, 33).toString('hex'));
            console.log('  Y 坐标:', publicKeyPoint.slice(33, 65).toString('hex'));
        } else {
            console.log('❌ 公钥格式异常');
        }
        
        // 5. 对比之前使用的公钥
        const previousPublicKey = 'BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=';
        const extractedPublicKey = publicKeyPoint.toString('base64');
        
        console.log('🔍 公钥对比:');
        console.log('  之前使用的:', previousPublicKey);
        console.log('  证书提取的:', extractedPublicKey);
        console.log('  是否一致:', previousPublicKey === extractedPublicKey ? '✅ 是' : '❌ 否');
        
        if (previousPublicKey !== extractedPublicKey) {
            console.log('\n🎯 发现问题！');
            console.log('您之前使用的服务器公钥与证书中的不一致！');
            console.log('这就是为什么 req_sign 不匹配的原因。');
            console.log('\n请使用证书中提取的公钥重新测试。');
        }
        
        return {
            spkiDer: spkiPublicKey,
            publicKeyPoint: publicKeyPoint,
            base64PublicKey: extractedPublicKey,
            hexPublicKey: publicKeyPoint.toString('hex')
        };
        
    } catch (error) {
        console.error('❌ 提取公钥失败:', error.message);
        return null;
    }
}

// 运行提取
const result = extractPublicKeyFromCert();

if (result) {
    console.log('\n📋 使用说明:');
    console.log('请将以下公钥用于 ECDH 密钥交换:');
    console.log(`const serverPublicKeyBase64 = '${result.base64PublicKey}';`);
}
