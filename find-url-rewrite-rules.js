const crypto = require('crypto');

// 直接使用我们已经验证的 clean 版本
const KeyGenerator = require('./node-key-generator-clean.js');

// 测试不同路径重写的签名结果
function testPathRewriteSignatures() {
    // 固定的测试数据
    const testData = {
        ticket: "hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=",
        timestamp: 1754020144, // 使用固定时间戳
        clientPrivateKeyPem: `************************************************************************************************************************************************************************************************************************************************`,
        serverPublicKeyBase64: "BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg="
    };

    // 可能的路径重写场景
    const pathScenarios = [
        { name: "原始路径", path: "/aweme/v1/web/comment/publish/" },
        { name: "去掉末尾斜杠", path: "/aweme/v1/web/comment/publish" },
        { name: "简化为 /publish/", path: "/publish/" },
        { name: "简化为 /publish", path: "/publish" },
        { name: "API 路径", path: "/api/comment/publish/" },
        { name: "API 路径无斜杠", path: "/api/comment/publish" },
        { name: "只有 comment", path: "/comment/" },
        { name: "只有 comment 无斜杠", path: "/comment" },
        { name: "Web API", path: "/web/comment/publish/" },
        { name: "抖音路径", path: "/douyin/comment/publish/" },
        { name: "V1 路径", path: "/v1/comment/publish/" }
    ];

    console.log('🔍 测试不同路径重写对签名的影响\n');

    const results = [];

    pathScenarios.forEach((scenario, index) => {
        console.log(`=== ${index + 1}. ${scenario.name}: ${scenario.path} ===`);
        
        // 构造签名数据
        const signData = `ticket=${testData.ticket}&path=${scenario.path}&timestamp=${testData.timestamp}`;
        console.log('sign_data:', signData);
        
        try {
            // 使用我们已经验证的 KeyGenerator 来生成签名
            // generateReqSign 是静态方法，需要传入完整参数
            const signature = KeyGenerator.generateReqSign(
                testData.clientPrivateKeyPem,
                testData.serverPublicKeyBase64,
                testData.ticket,
                scenario.path,
                testData.timestamp
            );
            
            console.log('req_sign:', signature);
            
            results.push({
                name: scenario.name,
                path: scenario.path,
                signData: signData,
                reqSign: signature
            });
            
        } catch (error) {
            console.log('❌ 签名生成失败:', error.message);
            results.push({
                name: scenario.name,
                path: scenario.path,
                signData: signData,
                reqSign: 'ERROR'
            });
        }
        
        console.log('');
    });

    // 输出汇总
    console.log('📊 所有路径的签名结果汇总:');
    console.log('='.repeat(80));
    results.forEach((result, index) => {
        console.log(`${index + 1}. ${result.name.padEnd(20)} | ${result.reqSign}`);
    });

    // 检查是否有重复的签名
    const signatures = results.map(r => r.reqSign).filter(s => s !== 'ERROR');
    const uniqueSignatures = [...new Set(signatures)];
    
    console.log(`\n🔍 发现 ${uniqueSignatures.length} 种不同的签名:`);
    uniqueSignatures.forEach((sig, index) => {
        const matchingPaths = results.filter(r => r.reqSign === sig).map(r => r.name);
        console.log(`${index + 1}. ${sig}`);
        console.log(`   对应路径: ${matchingPaths.join(', ')}`);
    });

    return results;
}

// 运行测试
const results = testPathRewriteSignatures();

// 提供已知的 TikTok 签名进行比较
console.log('\n🎯 与已知 TikTok 签名比较:');
console.log('TikTok 签名: aMGGQeXb2lxAOZfBSLmRnwMTI5s9VAWIv+HS5upaLXA=');
console.log('我们的签名: N1Oz4O1sxCoAW4lBkz0YJD2MD6SGR7wATgfTpvRYum0=');

const tikTokSignature = 'aMGGQeXb2lxAOZfBSLmRnwMTI5s9VAWIv+HS5upaLXA=';
const matchingResult = results.find(r => r.reqSign === tikTokSignature);

if (matchingResult) {
    console.log(`🎉 找到匹配! TikTok 使用的路径是: ${matchingResult.path}`);
    console.log(`   路径名称: ${matchingResult.name}`);
} else {
    console.log('❌ 没有找到匹配的路径重写规则');
    console.log('💡 可能需要尝试更多的路径变体或检查其他参数');
}
