const crypto = require('crypto');
const TikTokKeyGenerator = require('./node-key-generator-clean');

// 对比数据
const ourData = {
    "ts_sign": "ts.2.42722207c70de05af466562b3a8790d98d95622dec0bfc6c1310af9df3219d14c4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d",
    "req_content": "ticket,path,timestamp",
    "req_sign": "gilyAAy1kPUN7mGK9ah1dxue1ZAXhTmO7XgoEtf9Ccw=",
    "timestamp": 1754015899
};

const tiktokData = {
    "ts_sign": "ts.2.42722207c70de05af466562b3a8790d98d95622dec0bfc6c1310af9df3219d14c4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d",
    "req_content": "ticket,path,timestamp", 
    "req_sign": "YLxqIypIh2SV+fY6KLTvI9SHm6y6cXHSjrHOgBAH6Uc=",
    "timestamp": 1754013109
};

// 真实密钥对
const realKeyPair = {
************************************************************************************************************************************************************************************************************************************************************************
    "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----"
};

// 服务器公钥
const serverPublicKeyBase64 = 'BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=';

// 真实数据
const realData = {
    "ticket": "hash.t24l/ME/pgOrJ9YLZi3ZWKKbVCgA6/HjfV0tabzCVpw=",
    "ts_sign": "ts.2.42722207c70de05af466562b3a8790d98d95622dec0bfc6c1310af9df3219d14c4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d",
    "client_cert": "pub.BAYnZvHvp0G0t5hQB7/9w+/dvLOlo3cLeAFaOAiolNnc+zOVRwpAtyDG5HWTSfbX+0tzpwkIIZdXebIzGreR8RQ=",
    "log_id": "2025080109514949C272847163F86CF378",
    "create_time": 1754013109
};

function analyzeReqSignDifference() {
    console.log('🔍 分析 req_sign 差异\n');
    
    // 1. 基本信息对比
    console.log('📊 基本信息对比:');
    console.log('我们的数据:');
    console.log('  req_sign:', ourData.req_sign);
    console.log('  timestamp:', ourData.timestamp);
    console.log('  长度:', ourData.req_sign.length, '字符');
    
    console.log('\nTikTok 数据:');
    console.log('  req_sign:', tiktokData.req_sign);
    console.log('  timestamp:', tiktokData.timestamp);
    console.log('  长度:', tiktokData.req_sign.length, '字符');
    
    console.log('\n时间戳差异:', ourData.timestamp - tiktokData.timestamp, '秒');
    
    // 2. Base64 解码分析
    console.log('\n🔓 Base64 解码分析:');
    const ourSignBytes = Buffer.from(ourData.req_sign, 'base64');
    const tiktokSignBytes = Buffer.from(tiktokData.req_sign, 'base64');
    
    console.log('我们的签名字节:');
    console.log('  十六进制:', ourSignBytes.toString('hex'));
    console.log('  长度:', ourSignBytes.length, '字节');
    
    console.log('\nTikTok 签名字节:');
    console.log('  十六进制:', tiktokSignBytes.toString('hex'));
    console.log('  长度:', tiktokSignBytes.length, '字节');
    
    // 3. 字节差异分析
    console.log('\n📈 字节差异分析:');
    const maxLength = Math.max(ourSignBytes.length, tiktokSignBytes.length);
    let differentBytes = 0;
    
    for (let i = 0; i < maxLength; i++) {
        const ourByte = i < ourSignBytes.length ? ourSignBytes[i] : 'N/A';
        const tiktokByte = i < tiktokSignBytes.length ? tiktokSignBytes[i] : 'N/A';
        
        if (ourByte !== tiktokByte) {
            differentBytes++;
            console.log(`  位置 ${i}: 我们=${ourByte.toString(16).padStart(2, '0')} vs TikTok=${tiktokByte.toString(16).padStart(2, '0')}`);
        }
    }
    
    console.log(`\n不同字节数: ${differentBytes}/${maxLength}`);
    
    return { ourSignBytes, tiktokSignBytes };
}

function reverseEngineerTikTokSign() {
    console.log('\n🔬 逆向分析 TikTok 签名过程\n');
    
    const path = '/aweme/v1/web/comment/publish/';
    
    // 1. 重现我们的签名过程
    console.log('🔄 重现我们的签名过程:');
    try {
        const ourResult = TikTokKeyGenerator.generateReqSign(
            realKeyPair.ec_privateKey,
            serverPublicKeyBase64,
            realData.ticket,
            path,
            tiktokData.timestamp  // 使用 TikTok 的时间戳
        );
        
        console.log('  使用 TikTok 时间戳的结果:', ourResult.req_sign);
        console.log('  TikTok 实际结果:', tiktokData.req_sign);
        console.log('  是否匹配:', ourResult.req_sign === tiktokData.req_sign ? '✅ 是' : '❌ 否');
        
        if (ourResult.req_sign !== tiktokData.req_sign) {
            console.log('\n🔍 深度分析差异原因...');
            
            // 2. 分析可能的差异点
            console.log('\n可能的差异点:');
            console.log('1. ECDH 共享密钥计算');
            console.log('2. HKDF 密钥派生');
            console.log('3. 签名数据格式');
            console.log('4. HMAC 计算');
            console.log('5. Base64 编码');
            
            // 3. 尝试不同的签名数据格式
            console.log('\n🧪 测试不同的签名数据格式:');
            
            const variations = [
                `ticket=${realData.ticket}&path=${path}&timestamp=${tiktokData.timestamp}`,
                `ticket=${realData.ticket}&path=${encodeURIComponent(path)}&timestamp=${tiktokData.timestamp}`,
                `ticket=${encodeURIComponent(realData.ticket)}&path=${path}&timestamp=${tiktokData.timestamp}`,
                `ticket=${realData.ticket}&path=${path.replace(/\/$/, '')}&timestamp=${tiktokData.timestamp}`,
                `ticket=${realData.ticket}&path=${path}&timestamp=${tiktokData.timestamp}&`,
                `path=${path}&ticket=${realData.ticket}&timestamp=${tiktokData.timestamp}`,
                `timestamp=${tiktokData.timestamp}&ticket=${realData.ticket}&path=${path}`
            ];
            
            variations.forEach((variation, index) => {
                try {
                    const derivedKey = TikTokKeyGenerator.performECDH(
                        realKeyPair.ec_privateKey,
                        serverPublicKeyBase64
                    );
                    
                    const hmac = crypto.createHmac('sha256', derivedKey);
                    hmac.update(variation, 'utf8');
                    const signature = hmac.digest('base64');
                    
                    console.log(`  变体 ${index + 1}: ${variation}`);
                    console.log(`    结果: ${signature}`);
                    console.log(`    匹配: ${signature === tiktokData.req_sign ? '✅' : '❌'}`);
                    
                    if (signature === tiktokData.req_sign) {
                        console.log(`\n🎉 找到匹配的格式！变体 ${index + 1}`);
                        return signature;
                    }
                } catch (error) {
                    console.log(`    错误: ${error.message}`);
                }
            });
        }
        
        return ourResult;
        
    } catch (error) {
        console.error('❌ 签名过程失败:', error.message);
        return null;
    }
}

function testDifferentKeys() {
    console.log('\n🔑 测试不同密钥的可能性\n');
    
    // 检查是否使用了不同的客户端密钥
    console.log('检查客户端密钥一致性:');
    const extractedPublicKey = TikTokKeyGenerator.extractRawPublicKey(realKeyPair.ec_publicKey);
    const expectedPublicKey = realData.client_cert.replace('pub.', '');
    
    console.log('  提取的公钥:', extractedPublicKey);
    console.log('  期望的公钥:', expectedPublicKey);
    console.log('  密钥匹配:', extractedPublicKey === expectedPublicKey ? '✅' : '❌');
    
    if (extractedPublicKey !== expectedPublicKey) {
        console.log('⚠️ 客户端密钥不匹配！这可能是差异的原因。');
    }
    
    // 检查服务器公钥
    console.log('\n检查服务器公钥:');
    console.log('  使用的服务器公钥:', serverPublicKeyBase64);
    console.log('  长度:', serverPublicKeyBase64.length, '字符');
}

function generateDetailedReport() {
    console.log('\n📋 详细分析报告\n');
    
    const { ourSignBytes, tiktokSignBytes } = analyzeReqSignDifference();
    const signResult = reverseEngineerTikTokSign();
    testDifferentKeys();
    
    console.log('\n🎯 结论和建议:');
    
    if (ourSignBytes.length === tiktokSignBytes.length && ourSignBytes.length === 32) {
        console.log('✅ 签名长度正确 (32 字节，符合 HMAC-SHA256)');
    } else {
        console.log('❌ 签名长度异常');
    }
    
    console.log('\n可能的问题点:');
    console.log('1. 时间戳格式或精度');
    console.log('2. 签名数据的字符编码');
    console.log('3. ECDH 密钥交换的实现细节');
    console.log('4. HKDF 参数设置');
    console.log('5. 客户端或服务器密钥不匹配');
    
    console.log('\n下一步调试建议:');
    console.log('1. 确认使用完全相同的时间戳');
    console.log('2. 验证 ECDH 共享密钥是否一致');
    console.log('3. 检查 HKDF 派生密钥是否正确');
    console.log('4. 对比签名数据的字节级内容');
}

// 运行分析
generateDetailedReport();
