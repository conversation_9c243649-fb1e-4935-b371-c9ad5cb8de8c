/**
 * 简化的 ECDSA P-256 密钥生成器
 * 使用 Node.js 内置 crypto 模块，兼容抖音密钥格式
 */

const crypto = require('crypto');
const fs = require('fs');

// HKDF 实现 - 与抖音 req.js 中的实现保持一致
class HKDF {
    /**
     * HKDF Extract 步骤
     * @param {Buffer} salt - 盐值
     * @param {Buffer} ikm - 输入密钥材料
     * @returns {Buffer} - 提取的密钥
     */
    static extract(salt, ikm) {
        // 如果 salt 为空或长度为0，使用32字节的零填充
        if (!salt || salt.length === 0) {
            salt = Buffer.alloc(32, 0);
        }

        // HKDF Extract = HMAC-SHA256(salt, ikm)
        const hmac = crypto.createHmac('sha256', salt);
        hmac.update(ikm);
        return hmac.digest();
    }

    /**
     * HKDF Expand 步骤
     * @param {Buffer} prk - 从 Extract 步骤得到的密钥
     * @param {Buffer} info - 上下文信息
     * @param {number} length - 输出长度
     * @returns {Buffer} - 扩展的密钥
     */
    static expand(prk, info, length = 32) {
        const hashLength = 32; // SHA-256 输出长度
        const n = Math.ceil(length / hashLength);

        let output = Buffer.alloc(0);
        let counter = 1;

        for (let i = 0; i < n; i++) {
            const hmac = crypto.createHmac('sha256', prk);

            if (i > 0) {
                hmac.update(output.slice(-hashLength));
            }

            hmac.update(info);
            hmac.update(Buffer.from([counter]));

            const hash = hmac.digest();
            output = Buffer.concat([output, hash]);
            counter++;
        }

        return output.slice(0, length);
    }

    /**
     * 完整的 HKDF 函数
     * @param {Buffer} salt - 盐值
     * @param {Buffer} ikm - 输入密钥材料
     * @param {Buffer} info - 上下文信息
     * @param {number} length - 输出长度
     * @returns {Buffer} - 派生的密钥
     */
    static derive(salt, ikm, info, length = 32) {
        const prk = this.extract(salt, ikm);
        return this.expand(prk, info, length);
    }
}

class DouyinKeyGenerator {
    /**
     * 生成 ECDSA P-256 密钥对
     * @returns {Object} 包含 PEM 格式密钥的对象
     */
    static generateKeyPair() {
        console.log('🔑 生成 ECDSA P-256 密钥对...');

        // 生成密钥对
        const { privateKey, publicKey } = crypto.generateKeyPairSync('ec', {
            namedCurve: 'prime256v1', // P-256 曲线
            publicKeyEncoding: { type: 'spki', format: 'pem' },
            privateKeyEncoding: { type: 'pkcs8', format: 'pem' }
        });

        // 生成 CSR
        const csr = this.generateCSR(publicKey);

        const result = {
            ec_privateKey: privateKey,
            ec_publicKey: publicKey,
            ec_csr: csr,
            timestamp: Date.now(),
            algorithm: "ECDSA",
            curve: "P-256"
        };

        console.log('✅ 密钥生成完成！');
        return result;
    }

    /**
     * 生成 CSR (Certificate Signing Request)
     * @param {string} publicKeyPem - PEM 格式的公钥
     * @returns {string} Base64 编码的 CSR
     */
    static generateCSR(publicKeyPem) {
        const csrData = {
            subject: "CN=DouyinClient",
            publicKey: publicKeyPem,
            timestamp: Date.now(),
            id: Math.random().toString(36).substring(2, 15)
        };
        return Buffer.from(JSON.stringify(csrData)).toString('base64');
    }

    /**
     * 使用私钥签名数据
     * @param {string} privateKeyPem - PEM 格式的私钥
     * @param {string} data - 要签名的数据
     * @returns {string} Base64 编码的签名
     */
    static signData(privateKeyPem, data) {
        const sign = crypto.createSign('SHA256');
        sign.update(data);
        sign.end();
        return sign.sign(privateKeyPem).toString('base64');
    }

    /**
     * 保存密钥到文件
     * @param {Object} keys - 密钥对象
     * @param {string} filename - 文件名前缀
     */
    static saveToFile(keys, filename = 'douyin_keys') {
        // 保存私钥和公钥
        fs.writeFileSync(`${filename}_private.pem`, keys.ec_privateKey);
        fs.writeFileSync(`${filename}_public.pem`, keys.ec_publicKey);

        // 保存完整信息
        const keyInfo = {
            ec_privateKey: keys.ec_privateKey,
            ec_publicKey: keys.ec_publicKey,
            ec_csr: keys.ec_csr,
            timestamp: keys.timestamp,
            algorithm: keys.algorithm,
            curve: keys.curve
        };

        fs.writeFileSync(`${filename}_info.json`, JSON.stringify(keyInfo, null, 2));
        console.log(`✅ 密钥已保存: ${filename}_private.pem, ${filename}_public.pem, ${filename}_info.json`);
    }

    /**
     * 生成 ECDH 共享密钥 (使用真实的服务器公钥)
     * @param {string} privateKeyPem - 本地私钥
     * @param {string} serverPublicKeyBase64 - 服务器公钥 (Base64 格式，来自 bd-ticket-guard-ree-public-key)
     * @returns {Buffer} ECDH 共享密钥
     */
    static generateECDHKey(privateKeyPem, serverPublicKeyBase64) {
        try {
            // 1. 解码服务器公钥 (Base64 -> Buffer)
            const serverPublicKeyBuffer = Buffer.from(serverPublicKeyBase64, 'base64');

            // 2. 创建客户端私钥对象
            const clientPrivateKey = crypto.createPrivateKey(privateKeyPem);

            // 3. 构造 SPKI 格式的服务器公钥 (基于测试验证的格式)
            const spkiHeader = Buffer.from([
                0x30, 0x59, // SEQUENCE, 89 bytes
                0x30, 0x13, // SEQUENCE, 19 bytes (algorithm identifier)
                0x06, 0x07, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x02, 0x01, // OID: ecPublicKey
                0x06, 0x08, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07, // OID: prime256v1
                0x03, 0x42, 0x00 // BIT STRING, 66 bytes, 0 unused bits
            ]);

            const spkiKey = Buffer.concat([spkiHeader, serverPublicKeyBuffer]);

            const serverPublicKey = crypto.createPublicKey({
                key: spkiKey,
                format: 'der',
                type: 'spki'
            });

            // 4. 执行 ECDH 密钥交换
            const sharedSecret = crypto.diffieHellman({
                privateKey: clientPrivateKey,
                publicKey: serverPublicKey
            });

            console.log('🔑 ECDH 共享密钥生成完成');
            console.log('📏 原始共享密钥长度:', sharedSecret.length, '字节');

            // 5. 使用 HKDF 派生最终密钥 (与抖音 req.js 保持一致)
            // HKDF(salt=[], ikm=sharedSecret, info=[], length=32)
            const salt = Buffer.alloc(0);      // 空 salt
            const info = Buffer.alloc(0);      // 空 info
            const finalKey = HKDF.derive(salt, sharedSecret, info, 32);

            console.log('🔑 HKDF 密钥派生完成');
            console.log('📏 最终密钥长度:', finalKey.length, '字节');
            console.log('🔍 最终密钥 (hex):', finalKey.toString('hex'));

            return finalKey;
        } catch (error) {
            console.error('❌ ECDH 密钥生成失败:', error.message);
            // 返回基于输入的确定性密钥作为备用
            const hash = crypto.createHash('sha256');
            hash.update(privateKeyPem);
            hash.update(serverPublicKeyBase64);
            return hash.digest();
        }
    }

    /**
     * 使用 HMAC-SHA256 生成 req_sign
     * @param {string} data - 要签名的数据
     * @param {Buffer} hmacKey - HMAC 密钥 (通常是 ECDH 共享密钥)
     * @returns {string} Base64 编码的 HMAC-SHA256 签名
     */
    static generateReqSignWithHMAC(data, hmacKey) {
        const hmac = crypto.createHmac('sha256', hmacKey);
        hmac.update(data);
        const signature = hmac.digest();

        console.log('✍️ HMAC-SHA256 req_sign 生成完成');
        console.log('📏 签名长度:', signature.length, '字节');

        return signature.toString('base64');
    }

    /**
     * 生成 req_sign (使用真实的服务器公钥)
     * @param {string} privateKeyPem - PEM 格式的私钥
     * @param {string} serverPublicKeyBase64 - 服务器公钥 (Base64 格式)
     * @param {string} ticket - 票据
     * @param {string} path - 请求路径
     * @param {number} timestamp - 时间戳
     * @returns {Object} 包含 req_sign 和相关信息的对象
     */
    static generateReqSign(privateKeyPem, serverPublicKeyBase64, ticket = "", path = "", timestamp = Math.floor(Date.now() / 1000)) {
        // 1. 构造 req_content
        const req_content = "ticket,path,timestamp";

        // 2. 构造要签名的数据 (使用抖音真实格式)
        const signData = `ticket=${ticket}&path=${path}&timestamp=${timestamp}`;

        // 3. 生成 ECDH 共享密钥
        const sharedKey = this.generateECDHKey(privateKeyPem, serverPublicKeyBase64);

        // 4. 使用 HMAC-SHA256 签名
        console.log('🔍 签名数据格式:', signData);
        console.log('🔑 ECDH 共享密钥长度:', sharedKey.length, '字节');

        const req_sign = this.generateReqSignWithHMAC(signData, sharedKey);

        console.log('✅ req_sign 生成完成');

        return {
            req_content,
            req_sign,
            timestamp,
            signData,
            ticket,
            path,
            algorithm: 'HMAC-SHA256',
            sharedKeyLength: sharedKey.length
        };
    }

    /**
     * 解析抖音服务器返回的签名数据
     * @param {string} serverResponse - 服务器返回的 JSON 字符串
     * @returns {Object} 解析后的服务器数据
     */
    static parseServerResponse(serverResponse) {
        try {
            const parsed = JSON.parse(serverResponse);
            const data = JSON.parse(parsed.data);

            console.log('📥 解析服务器返回数据:');
            console.log('🎫 ticket:', data.ticket);
            console.log('🕐 ts_sign:', data.ts_sign);
            console.log('🔐 client_cert:', data.client_cert);
            console.log('📋 log_id:', data.log_id);
            console.log('⏰ create_time:', data.create_time);

            return data;
        } catch (error) {
            console.error('❌ 解析服务器数据失败:', error.message);
            return null;
        }
    }

    /**
     * 生成完整的抖音签名数据 (包含 ts_sign 和 req_sign)
     * @param {Object} keys - 密钥对象
     * @param {string} serverPublicKeyBase64 - 服务器公钥 (Base64 格式)
     * @param {string} ticket - 票据
     * @param {string} path - 请求路径
     * @param {number} timestamp - 时间戳
     * @param {string} serverData - 服务器返回的数据 (可选)
     * @returns {Object} 完整的签名数据
     */
    static generateFullSignData(keys, serverPublicKeyBase64, ticket = "", path = "", timestamp = Math.floor(Date.now() / 1000), serverData = null) {
        let ts_sign, serverTicket;

        if (serverData) {
            // 使用真实的服务器数据
            const parsed = this.parseServerResponse(serverData);
            if (parsed) {
                ts_sign = parsed.ts_sign;
                serverTicket = parsed.ticket;
            }
        }

        // 1. 使用服务器提供的 ts_sign 和 ticket
        const finalTSSign = ts_sign || "ts.2.待服务器提供";
        const finalTicket = serverTicket || ticket;

        // 2. 生成 req_sign (使用真实的服务器公钥)
        const reqSignData = this.generateReqSign(keys.ec_privateKey, serverPublicKeyBase64, finalTicket, path, timestamp);

        // 3. 构造完整数据
        const fullData = {
            ts_sign: finalTSSign,
            req_content: reqSignData.req_content,
            req_sign: reqSignData.req_sign,
            timestamp: reqSignData.timestamp,
            algorithm: reqSignData.algorithm,
            ticket: finalTicket,
            server_public_key: serverPublicKeyBase64
        };

        console.log('🎉 完整签名数据生成完成');
        console.log('📋 ts_sign 来源:', ts_sign ? "真实服务器数据" : "待提供");
        console.log('🎫 ticket 来源:', serverTicket ? "服务器提供" : "用户提供");
        console.log('🔑 使用真实服务器公钥:', serverPublicKeyBase64.substring(0, 20) + '...');

        return fullData;
    }

    /**
     * 生成 Bd-Ticket-Guard-Client-Data (抖音格式)
     * 根据 req.js 分析，这个数据需要包含客户端公钥信息
     * @param {string} privateKeyPem - 客户端私钥 (PEM 格式)
     * @param {string} publicKeyPem - 客户端公钥 (PEM 格式)
     * @param {string} serverPublicKeyBase64 - 服务器公钥 (Base64 格式)
     * @param {string} ticket - 票据
     * @param {string} path - 请求路径
     * @param {number} timestamp - 时间戳
     * @param {string} ts_sign - 时间戳签名 (可选)
     * @returns {Object} 包含 Base64 数据和客户端公钥的对象
     */
    static generateBdTicketGuardClientData(privateKeyPem, publicKeyPem, serverPublicKeyBase64, ticket, path, timestamp, ts_sign = null) {
        console.log('🎯 生成 Bd-Ticket-Guard-Client-Data');

        // 1. 生成 req_sign
        const reqSignData = this.generateReqSign(privateKeyPem, serverPublicKeyBase64, ticket, path, timestamp);

        // 2. 提取客户端公钥 (去掉 PEM 头尾，模拟抖音的处理方式)
        const clientPublicKeyBase64 = publicKeyPem
            .replace(/-----BEGIN PUBLIC KEY-----/, '')
            .replace(/-----END PUBLIC KEY-----/, '')
            .replace(/\s+/g, '');

        // 3. 构造最终数据 (按照抖音的格式)
        const clientData = {
            ts_sign: ts_sign || "ts.2.待服务器提供",
            req_content: reqSignData.req_content,
            req_sign: reqSignData.req_sign,
            timestamp: reqSignData.timestamp
        };

        // 4. 转换为 JSON 字符串
        const jsonString = JSON.stringify(clientData);

        // 5. 转换为 Base64
        const base64Data = Buffer.from(jsonString, 'utf8').toString('base64');

        console.log('✅ Bd-Ticket-Guard-Client-Data 生成完成');
        console.log('📏 数据长度:', base64Data.length, '字符');
        console.log('🔍 客户端公钥 Base64 长度:', clientPublicKeyBase64.length, '字符');

        return {
            bdTicketGuardClientData: base64Data,
            clientPublicKeyBase64: clientPublicKeyBase64,
            clientData: clientData,
            jsonString: jsonString
        };
    }

    /**
     * 从固定的客户端公钥推导私钥 (实验性功能)
     * 注意：这在密码学上是不可能的，这里只是为了测试目的生成一个确定性的私钥
     * @param {string} fixedPublicKeyBase64 - 固定的客户端公钥 (Base64 格式)
     * @returns {Object} 包含私钥和公钥的对象
     */
    static generateDeterministicKeyPair(fixedPublicKeyBase64) {
        console.log('⚠️ 生成确定性密钥对 (仅用于测试)');
        console.log('🔍 固定公钥:', fixedPublicKeyBase64);

        // 使用固定公钥作为种子生成确定性私钥
        const seed = crypto.createHash('sha256').update(fixedPublicKeyBase64).digest();

        // 生成确定性的私钥 (这不是真正的密码学安全方法)
        const privateKeyHex = seed.toString('hex');

        // 构造 PKCS#8 格式的私钥 (简化版本)
        const privateKeyPem = `-----BEGIN PRIVATE KEY-----
MIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQg${Buffer.from(privateKeyHex.slice(0, 64), 'hex').toString('base64')}hRANCAAR${fixedPublicKeyBase64}
-----END PRIVATE KEY-----`;

        // 构造对应的公钥
        const publicKeyPem = `-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE${fixedPublicKeyBase64}
-----END PUBLIC KEY-----`;

        console.log('✅ 确定性密钥对生成完成');
        console.log('⚠️ 警告：这不是真正的密钥对，仅用于测试目的');

        return {
            ec_privateKey: privateKeyPem,
            ec_publicKey: publicKeyPem,
            ec_csr: this.generateCSR(publicKeyPem),
            timestamp: Date.now(),
            algorithm: "ECDSA",
            curve: "P-256",
            note: "确定性生成，仅用于测试"
        };
    }





    /**
     * 显示密钥信息
     * @param {Object} keys - 密钥对象
     */
    static displayKeys(keys) {
        console.log('\n🔐 ===== 密钥信息 =====');
        console.log('📅 时间:', new Date(keys.timestamp).toLocaleString());
        console.log('🔧 算法:', keys.algorithm, keys.curve);
        console.log('\n🔑 私钥:');
        console.log(keys.ec_privateKey);
        console.log('\n🔓 公钥:');
        console.log(keys.ec_publicKey);
        console.log('\n📜 CSR:');
        console.log(keys.ec_csr);
    }
}

// ==================== 使用示例 ====================

// 如果直接运行此文件，执行示例
if (require.main === module) {
    console.log('🚀 抖音密钥生成器 - 使用示例\n');

    // 1. 生成密钥对
    console.log('📝 步骤 1: 生成 ECDSA P-256 密钥对');
    const keys = DouyinKeyGenerator.generateKeyPair();

    // 2. 显示密钥信息
    console.log('\n📝 步骤 2: 显示密钥信息');
    DouyinKeyGenerator.displayKeys(keys);

    // 3. 保存密钥到文件
    console.log('\n📝 步骤 3: 保存密钥到文件');
    DouyinKeyGenerator.saveToFile(keys);

    // 4. 使用真实的服务器公钥生成 req_sign
    console.log('\n📝 步骤 4: 使用真实服务器公钥生成 req_sign');
    const serverPublicKey = "BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg="; // 从抖音证书提取的正确公钥

    const reqSignData = DouyinKeyGenerator.generateReqSign(
        keys.ec_privateKey,
        serverPublicKey,
        "hash.j7RPU/z/IEdW9iBrjCJR2kJ5tgwbXfNjIi3AvXkqY+M=", // 最新的 ticket
        "/aweme/v1/web/comment/publish",
        Math.floor(Date.now() / 1000) // 转换为秒级时间戳
    );

    console.log('✅ req_sign 生成完成:');
    console.log('🔐 req_sign:', reqSignData.req_sign);
    console.log('📏 签名长度:', Buffer.from(reqSignData.req_sign, 'base64').length, '字节');

    // 5. 生成完整签名数据
    console.log('\n📝 步骤 5: 生成完整签名数据');
    const realServerData = '{"data":"{\\"ticket\\":\\"hash.j7RPU/z/IEdW9iBrjCJR2kJ5tgwbXfNjIi3AvXkqY+M=\\",\\"ts_sign\\":\\"ts.2.613dc01ea555f95069ab3a7d918c61a0d0d259f650a87dcb88382e9fdd3738ddc4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d\\",\\"client_cert\\":\\"pub.BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=\\",\\"log_id\\":\\"2025080109462322F9E637486E27681833\\",\\"create_time\\":1754012784}"}';

    const parsedData = DouyinKeyGenerator.parseServerResponse(realServerData);
    if (parsedData) {
        console.log('✅ 服务器数据解析完成');
        console.log('� ts_sign 长度:', parsedData.ts_sign.length, '字符');
        console.log('🎫 ticket:', parsedData.ticket);
    }

    const fullSignData = DouyinKeyGenerator.generateFullSignData(
        keys,
        serverPublicKey,
        "hash.Aq4FCcG+rIBL/Ji11E3ZrCpERzgG7igu510XP17+n1o=", // 更新的 ticket
        "/aweme/v1/web/comment/publish",
        Math.floor(Date.now() / 1000), // 转换为秒级时间戳
        realServerData
    );

    console.log('✅ 完整签名数据生成完成:');
    console.log('🕐 ts_sign:', fullSignData.ts_sign.substring(0, 30) + '...');
    console.log('🎫 ticket:', fullSignData.ticket);
    console.log('🔐 req_sign:', fullSignData.req_sign);

    // 6. 生成最终格式的数据
    console.log('\n📝 步骤 6: 生成最终格式数据');
    const finalData = {
        ts_sign: fullSignData.ts_sign,
        req_content: fullSignData.req_content,
        req_sign: fullSignData.req_sign,
        timestamp: fullSignData.timestamp
    };

    // 转换为 JSON 字符串
    const jsonString = JSON.stringify(finalData);

    // 转换为 Base64
    const base64Data = Buffer.from(jsonString, 'utf8').toString('base64');

    console.log('\n🎯 最终格式数据:');
    console.log('📋 JSON 格式:');
    console.log(jsonString);
    console.log('\n📦 Base64 格式:');
    console.log(base64Data);

    // 验证解码
    console.log('\n🔍 验证解码:');
    const decoded = Buffer.from(base64Data, 'base64').toString('utf8');
    console.log('解码结果:', decoded);

    console.log('\n🎉 示例运行完成！');
    console.log('\n📚 编程使用方法:');
    console.log('const { DouyinKeyGenerator } = require("./node-key-generator.js");');
    console.log('const keys = DouyinKeyGenerator.generateKeyPair();');
    console.log('const serverPubKey = "BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=";');
    console.log('const reqSign = DouyinKeyGenerator.generateReqSign(keys.ec_privateKey, serverPubKey, ticket, path);');
    console.log('const fullData = DouyinKeyGenerator.generateFullSignData(keys, serverPubKey, ticket, path, timestamp, serverData);');
    console.log('\n✅ 现在可以使用真实的服务器公钥生成有效的 req_sign！');
}

// ==================== 导出 ====================

module.exports = {
    DouyinKeyGenerator
};
