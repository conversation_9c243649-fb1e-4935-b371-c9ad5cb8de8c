const TikTokKeyGenerator = require('./node-key-generator-clean');

// 抖音真实数据
const realData = {
    "ts_sign": "ts.2.f85fa14375fff92cdd5ea6c8f793991858719360bcd893759a141ec72e687dc1c4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d",
    "req_content": "ticket,path,timestamp",
    "req_sign": "T/LQTj52YouxIHVpGUVI61z/HnwTZ+LvqNjfwcSQ7RY=",
    "timestamp": 1754026792
};

// 测试参数
const testParams = {
    ticket: "hash.lIyPEwMuuN78g2DGuW0hMK4jyPxpv5LN0Ak+vOJUZY=",
    timestamp: 1754026792,
    // 尝试不同的 path 格式
    paths: [
        "aweme/v1/web/comment/publish",
        "/aweme/v1/web/comment/publish",
        "/aweme/v1/web/comment/publish/",
        "aweme/v1/web/comment/publish/",
        "https://www.douyin.com/aweme/v1/web/comment/publish",
        "https://www.douyin.com/aweme/v1/web/comment/publish/"
    ]
};

// 真实密钥对
const realKeyPair = {
    "ec_privateKey": "-----B<PERSON>IN PRIVATE KEY-----\nMIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQgxVdowRX7ToHEavOnHi5IwvvCzYkpYsl0EC579P1baomhRANCAAQGJ2bx76dBtLeYUAe//cPv3byzpaN3C3gBWjgIqJTZ3PszlUcKQLcgxuR1k0n21/tLc6cJCCGXV3myMxq3kfEU\n-----END PRIVATE KEY-----",
    "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----"
};

// 服务器公钥
const serverPublicKeyBase64 = 'BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=';

function testPathProcessing() {
    console.log('🧪 测试抖音 path 处理方式\n');
    console.log('🎯 目标 req_sign:', realData.req_sign);
    console.log('📅 使用时间戳:', testParams.timestamp);
    console.log('🎫 使用 ticket:', testParams.ticket);
    console.log('');

    let foundMatch = false;

    testParams.paths.forEach((path, index) => {
        console.log(`\n📋 测试 ${index + 1}: ${path}`);
        console.log('─'.repeat(50));

        try {
            // 1. 测试 path 处理
            const processedPath = TikTokKeyGenerator.processPath(path);
            console.log('🛣️ 处理后的 path:', processedPath);

            // 2. 生成 req_sign
            const result = TikTokKeyGenerator.generateReqSign(
                realKeyPair.ec_privateKey,
                serverPublicKeyBase64,
                testParams.ticket,
                path,
                testParams.timestamp
            );

            console.log('📝 签名数据:', result.sign_data);
            console.log('🔐 生成的 req_sign:', result.req_sign);
            console.log('🎯 目标 req_sign:', realData.req_sign);
            
            const isMatch = result.req_sign === realData.req_sign;
            console.log('✅ 匹配结果:', isMatch ? '🎉 匹配!' : '❌ 不匹配');

            if (isMatch) {
                foundMatch = true;
                console.log('\n🎊 找到正确的 path 格式!');
                console.log('🛣️ 正确的 path:', path);
                console.log('🔄 处理后的 path:', processedPath);
                console.log('📝 正确的签名数据:', result.sign_data);
            }

        } catch (error) {
            console.log('❌ 错误:', error.message);
        }
    });

    if (!foundMatch) {
        console.log('\n🔍 未找到匹配的 path 格式，尝试其他可能性...');
        
        // 尝试一些特殊的 path 变体
        const specialPaths = [
            // 可能需要编码
            encodeURIComponent("aweme/v1/web/comment/publish"),
            encodeURI("/aweme/v1/web/comment/publish"),
            // 可能有特殊字符
            "/aweme/v1/web/comment/publish?",
            "/aweme/v1/web/comment/publish#",
            // 可能是其他格式
            "aweme.v1.web.comment.publish",
            "/api/aweme/v1/web/comment/publish"
        ];

        specialPaths.forEach((path, index) => {
            try {
                const result = TikTokKeyGenerator.generateReqSign(
                    realKeyPair.ec_privateKey,
                    serverPublicKeyBase64,
                    testParams.ticket,
                    path,
                    testParams.timestamp
                );

                console.log(`\n特殊测试 ${index + 1}: ${path}`);
                console.log('🔐 req_sign:', result.req_sign);
                console.log('✅ 匹配:', result.req_sign === realData.req_sign ? '🎉 匹配!' : '❌ 不匹配');

                if (result.req_sign === realData.req_sign) {
                    console.log('\n🎊 找到正确的特殊 path 格式!');
                    foundMatch = true;
                }
            } catch (error) {
                console.log(`特殊测试 ${index + 1} 错误:`, error.message);
            }
        });
    }

    return foundMatch;
}

function analyzeSignatureComponents() {
    console.log('\n🔬 分析签名组件\n');

    // 使用最可能的 path 格式
    const likelyPath = "/aweme/v1/web/comment/publish";
    
    try {
        // 1. 获取 ECDH 密钥
        const derivedKey = TikTokKeyGenerator.performECDH(
            realKeyPair.ec_privateKey,
            serverPublicKeyBase64
        );
        
        console.log('🔑 ECDH 派生密钥 (hex):', derivedKey.toString('hex'));
        console.log('🔑 ECDH 派生密钥长度:', derivedKey.length, '字节');

        // 2. 构造签名数据
        const signData = `ticket=${testParams.ticket}&path=${likelyPath}&timestamp=${testParams.timestamp}`;
        console.log('📝 签名数据:', signData);
        console.log('📏 签名数据长度:', signData.length, '字符');

        // 3. 计算 HMAC
        const crypto = require('crypto');
        const hmac = crypto.createHmac('sha256', derivedKey);
        hmac.update(signData, 'utf8');
        const signature = hmac.digest();
        
        console.log('🔐 HMAC 原始签名 (hex):', signature.toString('hex'));
        console.log('🔐 HMAC 签名长度:', signature.length, '字节');
        
        const base64Signature = signature.toString('base64');
        console.log('📦 Base64 签名:', base64Signature);
        console.log('🎯 目标签名:', realData.req_sign);
        console.log('✅ 匹配:', base64Signature === realData.req_sign ? '🎉 是' : '❌ 否');

    } catch (error) {
        console.error('❌ 分析失败:', error.message);
    }
}

// 运行测试
console.log('🚀 开始测试抖音 path 处理方式\n');
const foundMatch = testPathProcessing();

if (!foundMatch) {
    analyzeSignatureComponents();
}

console.log('\n📋 测试完成');
