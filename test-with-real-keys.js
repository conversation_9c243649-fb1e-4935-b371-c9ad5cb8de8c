const TikTokKeyGenerator = require('./node-key-generator-clean');
const crypto = require('crypto');

// 抖音真实的客户端密钥对
const realClientKeys = {
************************************************************************************************************************************************************************************************************************************************************************
    "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----"
};

// 从证书中提取的真实服务器公钥
function extractServerPublicKeyFromCert() {
    const serverCert = `-----BEGIN CERTIFICATE-----
MIIEfTCCBCKgAwIBAgIUXWdS2tzmSoewCWfKFyiWMrJqs/0wCgYIKoZIzj0EAwIw
MTELMAkGA1UEBhMCQ04xIjAgBgNVBAMMGXRpY2tldF9ndWFyZF9jYV9lY2RzYV8y
NTYwIBcNMjIxMTE4MDUyMDA2WhgPMjA2OTEyMzExNjAwMDBaMCQxCzAJBgNVBAYT
AkNOMRUwEwYDVQQDEwxlY2llcy1zZXJ2ZXIwWTATBgcqhkjOPQIBBggqhkjOPQMB
BwNCAASE2llDPlfc8Rq+5J5HXhg4edFjPnCF3Ua7JBoiE/foP9m7L5ELIcvxCgEx
aRCHbQ8kCCK/ArZ4FX/qCobZAkToo4IDITCCAx0wDgYDVR0PAQH/BAQDAgWgMDEG
A1UdJQQqMCgGCCsGAQUFBwMBBggrBgEFBQcDAgYIKwYBBQUHAwMGCCsGAQUFBwME
MCkGA1UdDgQiBCABydxqGrVEHhtkCWTb/vicGpDZPFPDxv82wiuywUlkBDArBgNV
HSMEJDAigCAypWfqjmRIEo3MTk1Ae3MUm0dtU3qk0YDXeZSXeyJHgzCCAZQGCCsG
AQUFBwEBBIIBhjCCAYIwRgYIKwYBBQUHMAGGOmh0dHA6Ly9uZXh1cy1wcm9kdWN0
aW9uLmJ5dGVkYW5jZS5jb20vYXBpL2NlcnRpZmljYXRlL29jc3AwRgYIKwYBBQUH
MAGGOmh0dHA6Ly9uZXh1cy1wcm9kdWN0aW9uLmJ5dGVkYW5jZS5uZXQvYXBpL2Nl
cnRpZmljYXRlL29jc3AwdwYIKwYBBQUHMAKGa2h0dHA6Ly9uZXh1cy1wcm9kdWN0
aW9uLmJ5dGVkYW5jZS5jb20vYXBpL2NlcnRpZmljYXRlL2Rvd25sb2FkLzQ4RjlD
MEU3QjBDNUE3MDVCOTgyQkU1NTE3MDVGNjQ1QzhDODc4QTguY3J0MHcGCCsGAQUF
BzAChmtodHRwOi8vbmV4dXMtcHJvZHVjdGlvbi5ieXRlZGFuY2UubmV0L2FwaS9j
ZXJ0aWZpY2F0ZS9kb3dubG9hZC80OEY5QzBFN0IwQzVBNzA1Qjk4MkJFNTUxNzA1
RjY0NUM4Qzg3OEE4LmNydDCB5wYDVR0fBIHfMIHcMGygaqBohmZodHRwOi8vbmV4
dXMtcHJvZHVjdGlvbi5ieXRlZGFuY2UuY29tL2FwaS9jZXJ0aWZpY2F0ZS9jcmwv
NDhGOUMwRTdCMEM1QTcwNUI5ODJCRTU1MTcwNUY2NDVDOEM4NzhBOC5jcmwwbKBq
oGiGZmh0dHA6Ly9uZXh1cy1wcm9kdWN0aW9uLmJ5dGVkYW5jZS5uZXQvYXBpL2Nl
cnRpZmljYXRlL2NybC80OEY5QzBFN0IwQzVBNzA1Qjk4MkJFNTUxNzA1RjY0NUM4
Qzg3OEE4LmNybDAKBggqhkjOPQQDAgNJADBGAiEAqMjT5ADMdGMeaImoJK4J9jzE
LqZ573rNjsT3k14pK50CIQCLpWHVKWi71qqqrMjiSDvUhpyO1DpTPRHlavPRuaNm
ww==
-----END CERTIFICATE-----`;

    try {
        const cert = crypto.createPublicKey({
            key: serverCert,
            format: 'pem',
            type: 'cert'
        });
        
        const spkiPublicKey = cert.export({
            format: 'der',
            type: 'spki'
        });
        
        // 提取公钥点（去掉 SPKI 头部 26 字节）
        const publicKeyPoint = spkiPublicKey.slice(26);
        return publicKeyPoint.toString('base64');
        
    } catch (error) {
        console.error('❌ 提取服务器公钥失败:', error.message);
        return null;
    }
}

function testWithRealKeys() {
    console.log('🧪 使用抖音真实密钥测试 req_sign 生成\n');
    
    // 1. 提取真实的服务器公钥
    const realServerPublicKey = extractServerPublicKeyFromCert();
    if (!realServerPublicKey) {
        console.error('❌ 无法提取服务器公钥');
        return;
    }
    
    console.log('🔑 密钥信息:');
    console.log('  服务器公钥 (base64):', realServerPublicKey);
    console.log('  客户端私钥: [已加载]');
    console.log('');
    
    // 2. 抖音真实数据
    const testCases = [
        {
            name: "测试案例 1",
            ticket: "hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=",
            path: "/aweme/v1/web/comment/publish",
            timestamp: 1754030401,
            expectedReqSign: "FKpW/Dg8ry/tOJqu/9kHnfHAbzusq1f+YXesc3jkqeU="
        },
        {
            name: "测试案例 2", 
            ticket: "hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=",
            path: "/aweme/v1/web/comment/publish",
            timestamp: 1754030433,
            expectedReqSign: "T/LQTj52YouxIHVpGUVI61z/HnwTZ+LvqNjfwcSQ7RY="
        }
    ];
    
    testCases.forEach((testCase, index) => {
        console.log(`📋 ${testCase.name}:`);
        console.log('─'.repeat(50));
        
        try {
            // 生成 req_sign
            const result = TikTokKeyGenerator.generateReqSign(
                realClientKeys.ec_privateKey,
                realServerPublicKey,
                testCase.ticket,
                testCase.path,
                testCase.timestamp
            );
            
            console.log('📝 签名数据:', result.sign_data);
            console.log('🔐 生成的 req_sign:', result.req_sign);
            console.log('🎯 期望的 req_sign:', testCase.expectedReqSign);
            
            const isMatch = result.req_sign === testCase.expectedReqSign;
            console.log('✅ 匹配结果:', isMatch ? '🎉 完全匹配!' : '❌ 不匹配');
            
            if (!isMatch) {
                console.log('\n🔍 详细分析:');
                
                // 分析字节差异
                const ourBytes = Buffer.from(result.req_sign, 'base64');
                const expectedBytes = Buffer.from(testCase.expectedReqSign, 'base64');
                
                console.log('  我们的签名 (hex):', ourBytes.toString('hex'));
                console.log('  期望的签名 (hex):', expectedBytes.toString('hex'));
                console.log('  长度对比:', ourBytes.length, 'vs', expectedBytes.length);
                
                // 逐字节对比
                let differentBytes = 0;
                const maxLength = Math.max(ourBytes.length, expectedBytes.length);
                for (let i = 0; i < maxLength; i++) {
                    const ourByte = i < ourBytes.length ? ourBytes[i] : 'N/A';
                    const expectedByte = i < expectedBytes.length ? expectedBytes[i] : 'N/A';
                    if (ourByte !== expectedByte) {
                        differentBytes++;
                        if (differentBytes <= 5) { // 只显示前5个差异
                            console.log(`  位置 ${i}: 我们=${ourByte.toString(16).padStart(2, '0')} vs 期望=${expectedByte.toString(16).padStart(2, '0')}`);
                        }
                    }
                }
                console.log(`  不同字节数: ${differentBytes}/${maxLength}`);
            }
            
        } catch (error) {
            console.error('❌ 测试失败:', error.message);
        }
        
        console.log('');
    });
}

function debugECDHProcess() {
    console.log('🔬 调试 ECDH 密钥派生过程\n');
    
    const realServerPublicKey = extractServerPublicKeyFromCert();
    if (!realServerPublicKey) return;
    
    try {
        // 获取 ECDH 派生密钥
        const derivedKey = TikTokKeyGenerator.performECDH(
            realClientKeys.ec_privateKey,
            realServerPublicKey
        );
        
        console.log('🔑 ECDH 密钥信息:');
        console.log('  派生密钥 (hex):', derivedKey.toString('hex'));
        console.log('  派生密钥 (base64):', derivedKey.toString('base64'));
        console.log('  密钥长度:', derivedKey.length, '字节');
        console.log('');
        
        // 手动计算 HMAC
        const testData = "ticket=hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=&path=/aweme/v1/web/comment/publish&timestamp=1754030401";
        
        const hmac = crypto.createHmac('sha256', derivedKey);
        hmac.update(testData, 'utf8');
        const signature = hmac.digest();
        
        console.log('🔐 HMAC 计算:');
        console.log('  输入数据:', testData);
        console.log('  HMAC 结果 (hex):', signature.toString('hex'));
        console.log('  HMAC 结果 (base64):', signature.toString('base64'));
        console.log('  期望结果:', 'FKpW/Dg8ry/tOJqu/9kHnfHAbzusq1f+YXesc3jkqeU=');
        
    } catch (error) {
        console.error('❌ 调试失败:', error.message);
    }
}

// 运行测试
testWithRealKeys();
debugECDHProcess();
