// 分析抖音提供的数据，寻找可能遗漏的信息

console.log('🔍 分析抖音数据中的线索\n');

// 您提供的两组抖音数据
const tiktokData = [
    {
        name: "数据组 1",
        timestamp: 1754030401,
        req_sign: "FKpW/Dg8ry/tOJqu/9kHnfHAbzusq1f+YXesc3jkqeU=",
        ticket: "hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=",
        log_id: "20250801143535858164FF9A5A62A0FD53"
    },
    {
        name: "数据组 2", 
        timestamp: 1754030433,
        req_sign: "T/LQTj52YouxIHVpGUVI61z/HnwTZ+LvqNjfwcSQ7RY=",
        ticket: "hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=",
        log_id: "20250801143535858164FF9A5A62A0FD53" // 假设相同
    }
];

// 我们生成的数据
const ourData = [
    {
        name: "我们的数据 1",
        timestamp: 1754030401,
        req_sign: "dR+8TTF6l/lRmRq7T1hLJeQGfUqML0aCyehEBpzJZQM=",
        ecdh_shared_key: "ca03d1f276b4c934935d4ebd2135441a4fa96ae945e03afffb8af2500a9240b1",
        hkdf_derived_key: "fa2f0939dcf3925bbad568c222bacce0424530753654e329919c7d8318fde756"
    },
    {
        name: "我们的数据 2",
        timestamp: 1754030433, 
        req_sign: "h2xsE6DW/kbmHS0b6zH6gHCPqpM/7hGRPemEwJbagoU=",
        ecdh_shared_key: "ca03d1f276b4c934935d4ebd2135441a4fa96ae945e03afffb8af2500a9240b1", // 应该相同
        hkdf_derived_key: "fa2f0939dcf3925bbad568c222bacce0424530753654e329919c7d8318fde756"  // 应该相同
    }
];

console.log('📊 数据对比分析:');
console.log('─'.repeat(80));

tiktokData.forEach((tiktok, index) => {
    const our = ourData[index];
    
    console.log(`\n${tiktok.name} vs ${our.name}:`);
    console.log(`  时间戳: ${tiktok.timestamp} (相同: ${tiktok.timestamp === our.timestamp})`);
    console.log(`  抖音 req_sign: ${tiktok.req_sign}`);
    console.log(`  我们 req_sign: ${our.req_sign}`);
    console.log(`  匹配: ${tiktok.req_sign === our.req_sign ? '✅' : '❌'}`);
    
    // 分析签名的字节差异
    const tiktokBytes = Buffer.from(tiktok.req_sign, 'base64');
    const ourBytes = Buffer.from(our.req_sign, 'base64');
    
    console.log(`  抖音签名 (hex): ${tiktokBytes.toString('hex')}`);
    console.log(`  我们签名 (hex): ${ourBytes.toString('hex')}`);
});

console.log('\n🤔 可能的问题分析:');
console.log('─'.repeat(50));

console.log('\n1. 客户端密钥问题:');
console.log('   - 我们使用的客户端私钥可能不是抖音实际使用的');
console.log('   - 抖音可能在每次会话中生成新的客户端密钥对');
console.log('   - 需要从浏览器开发者工具中获取实际使用的密钥');

console.log('\n2. ECDH 实现差异:');
console.log('   - Web Crypto API vs Node.js crypto 可能有细微差异');
console.log('   - 字节序问题 (大端序 vs 小端序)');
console.log('   - 密钥格式转换问题');

console.log('\n3. HKDF 实现差异:');
console.log('   - 虽然都遵循 RFC 5869，但实现可能有差异');
console.log('   - salt 和 info 参数的处理可能不同');

console.log('\n4. 隐藏参数:');
console.log('   - 可能还有其他未知的参数参与签名');
console.log('   - 比如 session ID、device ID 等');

console.log('\n🎯 下一步调试建议:');
console.log('─'.repeat(50));

console.log('\n1. 获取真实的客户端密钥:');
console.log('   - 在浏览器开发者工具中查看 localStorage');
console.log('   - 查找包含 ec_privateKey 的数据');
console.log('   - 确保使用抖音实际生成的密钥对');

console.log('\n2. 对比 ECDH 共享密钥:');
console.log('   - 如果能获取抖音的 ECDH 共享密钥，直接对比');
console.log('   - 验证我们的 ECDH 实现是否正确');

console.log('\n3. 验证签名数据格式:');
console.log('   - 确认签名数据的确切格式');
console.log('   - 检查是否有额外的参数或不同的编码');

console.log('\n4. 使用浏览器环境测试:');
console.log('   - 在浏览器中使用 Web Crypto API 进行测试');
console.log('   - 与 Node.js 环境的结果进行对比');

// 计算期望的 ECDH 共享密钥（反推）
console.log('\n🔬 反推分析:');
console.log('─'.repeat(30));

console.log('\n如果我们知道期望的 req_sign，可以尝试反推:');
console.log('1. 期望的 req_sign: FKpW/Dg8ry/tOJqu/9kHnfHAbzusq1f+YXesc3jkqeU=');
console.log('2. 签名数据: ticket=hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=&path=/aweme/v1/web/comment/publish&timestamp=1754030401');
console.log('3. 需要找到能产生这个 HMAC 结果的 HKDF 密钥');
console.log('4. 然后反推出正确的 ECDH 共享密钥');
console.log('5. 最终确定正确的客户端私钥');

console.log('\n💡 关键提示:');
console.log('问题很可能在于我们使用的客户端私钥不是抖音实际使用的！');
console.log('请检查浏览器中的实际密钥数据。');
