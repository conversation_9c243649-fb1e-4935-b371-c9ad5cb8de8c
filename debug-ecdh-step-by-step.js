const crypto = require('crypto');

// 抖音真实的客户端密钥对
const realClientKeys = {
************************************************************************************************************************************************************************************************************************************************************************
    "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----"
};

// 从证书提取的服务器公钥
const serverPublicKeyBase64 = "BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg=";

function debugKeyFormats() {
    console.log('🔍 调试密钥格式\n');
    
    try {
        // 1. 分析客户端私钥
        console.log('📋 客户端私钥分析:');
        const clientPrivateKey = crypto.createPrivateKey({
            key: realClientKeys.ec_privateKey,
            format: 'pem'
        });
        
        const clientPrivateKeyDer = clientPrivateKey.export({
            format: 'der',
            type: 'pkcs8'
        });
        
        console.log('  格式: PKCS#8 PEM');
        console.log('  DER 长度:', clientPrivateKeyDer.length, '字节');
        console.log('  DER (hex):', clientPrivateKeyDer.toString('hex'));
        
        // 2. 分析客户端公钥
        console.log('\n📋 客户端公钥分析:');
        const clientPublicKey = crypto.createPublicKey({
            key: realClientKeys.ec_publicKey,
            format: 'pem'
        });
        
        const clientPublicKeyDer = clientPublicKey.export({
            format: 'der',
            type: 'spki'
        });
        
        const clientPublicKeyPoint = clientPublicKeyDer.slice(26); // 去掉 SPKI 头部
        
        console.log('  格式: SPKI PEM');
        console.log('  SPKI DER 长度:', clientPublicKeyDer.length, '字节');
        console.log('  公钥点长度:', clientPublicKeyPoint.length, '字节');
        console.log('  公钥点 (hex):', clientPublicKeyPoint.toString('hex'));
        console.log('  公钥点 (base64):', clientPublicKeyPoint.toString('base64'));
        
        // 3. 分析服务器公钥
        console.log('\n📋 服务器公钥分析:');
        const serverPublicKeyBytes = Buffer.from(serverPublicKeyBase64, 'base64');
        
        console.log('  格式: 原始公钥点');
        console.log('  长度:', serverPublicKeyBytes.length, '字节');
        console.log('  公钥点 (hex):', serverPublicKeyBytes.toString('hex'));
        console.log('  公钥点 (base64):', serverPublicKeyBytes.toString('base64'));
        
        // 验证格式
        if (serverPublicKeyBytes.length === 65 && serverPublicKeyBytes[0] === 0x04) {
            console.log('  ✅ 格式验证: 未压缩点 (04 前缀)');
            console.log('  X 坐标:', serverPublicKeyBytes.slice(1, 33).toString('hex'));
            console.log('  Y 坐标:', serverPublicKeyBytes.slice(33, 65).toString('hex'));
        } else {
            console.log('  ❌ 格式验证失败');
        }
        
        return {
            clientPrivateKey,
            clientPublicKey,
            serverPublicKeyBytes
        };
        
    } catch (error) {
        console.error('❌ 密钥格式分析失败:', error.message);
        return null;
    }
}

function debugECDHWithWebCrypto() {
    console.log('\n🔬 使用 Web Crypto API 风格的 ECDH\n');
    
    const keys = debugKeyFormats();
    if (!keys) return;
    
    try {
        // 模拟 Web Crypto API 的 ECDH
        // 在 Node.js 中，我们需要手动实现类似的逻辑
        
        // 1. 创建服务器公钥对象
        const serverPublicKeySpki = Buffer.concat([
            // SPKI 头部 (26 字节)
            Buffer.from([
                0x30, 0x59, // SEQUENCE, 89 bytes
                0x30, 0x13, // SEQUENCE, 19 bytes
                0x06, 0x07, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x02, 0x01, // OID: ecPublicKey
                0x06, 0x08, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07, // OID: prime256v1
                0x03, 0x42, 0x00 // BIT STRING, 66 bytes, 0 unused bits
            ]),
            keys.serverPublicKeyBytes
        ]);
        
        const serverPublicKey = crypto.createPublicKey({
            key: serverPublicKeySpki,
            format: 'der',
            type: 'spki'
        });
        
        console.log('📋 服务器公钥重构:');
        console.log('  SPKI 长度:', serverPublicKeySpki.length, '字节');
        console.log('  SPKI (hex):', serverPublicKeySpki.toString('hex'));
        
        // 2. 执行 ECDH
        const sharedSecret = crypto.diffieHellman({
            privateKey: keys.clientPrivateKey,
            publicKey: serverPublicKey
        });
        
        console.log('\n🔑 ECDH 共享密钥:');
        console.log('  长度:', sharedSecret.length, '字节');
        console.log('  共享密钥 (hex):', sharedSecret.toString('hex'));
        console.log('  共享密钥 (base64):', sharedSecret.toString('base64'));
        
        // 3. 手动实现 HKDF (RFC 5869)
        const hkdfKey = hkdfExpand(hkdfExtract(Buffer.alloc(0), sharedSecret), Buffer.alloc(0), 32);
        
        console.log('\n🔐 HKDF 派生密钥:');
        console.log('  长度:', hkdfKey.length, '字节');
        console.log('  派生密钥 (hex):', hkdfKey.toString('hex'));
        console.log('  派生密钥 (base64):', hkdfKey.toString('base64'));
        
        // 4. 计算 HMAC
        const testData = "ticket=hash.aB2DXOilCsHpT0QdSVerpT5RlKFXM8JjGPQlZnHXQao=&path=/aweme/v1/web/comment/publish&timestamp=1754030401";
        
        const hmac = crypto.createHmac('sha256', hkdfKey);
        hmac.update(testData, 'utf8');
        const signature = hmac.digest();
        
        console.log('\n🔐 HMAC 签名:');
        console.log('  输入数据:', testData);
        console.log('  HMAC (hex):', signature.toString('hex'));
        console.log('  HMAC (base64):', signature.toString('base64'));
        console.log('  期望结果:', 'FKpW/Dg8ry/tOJqu/9kHnfHAbzusq1f+YXesc3jkqeU=');
        console.log('  匹配:', signature.toString('base64') === 'FKpW/Dg8ry/tOJqu/9kHnfHAbzusq1f+YXesc3jkqeU=' ? '✅ 是' : '❌ 否');
        
    } catch (error) {
        console.error('❌ ECDH 调试失败:', error.message);
    }
}

// RFC 5869 HKDF 实现
function hkdfExtract(salt, ikm) {
    if (salt.length === 0) {
        salt = Buffer.alloc(32); // SHA-256 hash length
    }
    const hmac = crypto.createHmac('sha256', salt);
    hmac.update(ikm);
    return hmac.digest();
}

function hkdfExpand(prk, info, length) {
    const hashLength = 32; // SHA-256
    const n = Math.ceil(length / hashLength);
    let okm = Buffer.alloc(0);
    let t = Buffer.alloc(0);
    
    for (let i = 1; i <= n; i++) {
        const hmac = crypto.createHmac('sha256', prk);
        hmac.update(t);
        hmac.update(info);
        hmac.update(Buffer.from([i]));
        t = hmac.digest();
        okm = Buffer.concat([okm, t]);
    }
    
    return okm.slice(0, length);
}

// 运行调试
debugECDHWithWebCrypto();
